DROP TABLE dws_biz_hl.dws_wi_biz_houselease_project_d;

CREATE TABLE IF NOT EXISTS dws_biz_hl.dws_wi_biz_houselease_project_d (
    -- 项目基本信息
    project_id string COMMENT '项目编号',
    project_name string COMMENT '项目名称',
    asset_cate string COMMENT '资产类别',
    project_status string COMMENT '项目当前状态_系统值',
    is_listing string COMMENT '是否披露中',
    is_dealed string COMMENT '是否已成交',
    prj_manager string COMMENT '项目负责人',
    prj_department string COMMENT '项目所属部门',
    prj_department_claim string COMMENT '项目认领部门',
    lessee_member string COMMENT '承租方受托交易服务会员',
    lessor_member string COMMENT '出租方受托交易服务会员',
    is_state_lease string COMMENT '是否国有房屋出租',
    is_mandatory_entry string COMMENT '是否强制进场_市属',
    is_priority_right string COMMENT '是否存在优先承租权',
    priority_lessee_nm string COMMENT '有优先承租权的原承租方名称',
    is_operate string COMMENT '是否运营房源信息',
    usage_require string COMMENT '房产使用用途要求',
    usage_state string COMMENT '房屋使用现状',
    is_pre_list string COMMENT '是否有预披露项目',
    margin_money decimal(20,4) COMMENT '保证金金额（元）',
    intend_lessee_cnt int COMMENT '拟征集承租方个数',
    pick_method string COMMENT '遴选方式（交易方式）',
    online_bid_type string COMMENT '网络竞价类型',
    is_esign string COMMENT '是否需要电签',
    
    -- 房屋维度统计字段
    location_cnt_house int COMMENT '【房屋】坐落位置数量',
    province_cnt_house int COMMENT '【房屋】省份数量',
    city_cnt_house int COMMENT '【房屋】城市数量',
    district_cnt_house int COMMENT '【房屋】市辖区数量',
    street_cnt_house int COMMENT '【房屋】街道数量',
    biz_district_cnt_house int COMMENT '【房屋】商圈数量',
    area_sum_house decimal(12,4) COMMENT '【房屋】总建筑面积（平方米）',
    land_right_cnt_house int COMMENT '【房屋】土地使用权性质数量',
    asset_class_cnt_house int COMMENT '【房屋】资产分类数量',
    province_montage_house string COMMENT '【房屋】省份_拼接',
    city_montage_house string COMMENT '【房屋】城市_拼接',
    district_montage_house string COMMENT '【房屋】市辖区_拼接',
    street_montage_house string COMMENT '【房屋】街道_拼接',
    biz_district_montage_house string COMMENT '【房屋】商圈_拼接',
    land_right_montage_house string COMMENT '【房屋】土地使用权性质_拼接',
    asset_class_montage_house string COMMENT '【房屋】资产分类_拼接',
    
    -- 土地维度字段
    is_land string COMMENT '【土地】是否有土地',
    location_cnt_land int COMMENT '【土地】坐落位置数量',
    area_sum_land decimal(12,4) COMMENT '【土地】总土地面积（平方米）',
    
    -- 其他资产类别
    is_machine string COMMENT '【机械设备】是否有机械设备',
    machine_nm string COMMENT '【机械设备】机械设备名称_拼接',
    is_vehicle string COMMENT '【交通运输工具】是否有交通运输工具',
    is_aircraft string COMMENT '【交通运输工具】是否有飞行器',
    vehicle_num int COMMENT '【交通运输工具】交通运输工具数量',
    is_other_asset string COMMENT '【其他资产】是否有其他资产',
    other_asset_nm string COMMENT '【其他资产】其他资产名称_拼接',
    
    -- 基本信息MAP结构
    basic_info_house map<string,map<string,string>> COMMENT '【房屋】房屋基本信息',
    basic_info_land map<string,map<string,string>> COMMENT '【土地】土地基本信息',
    
    -- 挂牌时间相关
    list_start_date string COMMENT '当前挂牌开始日期',
    list_end_date string COMMENT '当前挂牌结束日期',
    list_start_year string COMMENT '当前挂牌开始年份',
    list_end_year string COMMENT '当前挂牌结束年份',
    list_start_ym string COMMENT '当前挂牌开始年月',
    list_end_ym string COMMENT '当前挂牌结束年月',
    
    -- 标准化天数
    annual_days int COMMENT '标准化年天数（天）',
    monthly_days int COMMENT '标准化月天数（天）',
    
    -- 价格相关
    is_sep_price string COMMENT '是否分别计价',
    list_price_uni decimal(20,4) COMMENT '挂牌价格_统一计价录入值',
    list_price_unit_uni string COMMENT '挂牌价格单位_统一计价录入值',
    list_price_other_unit_uni string COMMENT '挂牌价格_其他单位录入值',
    list_price_sep map<string,string> COMMENT '挂牌价格_分别计价录入值',
    list_price_std_sq_d decimal(20,4) COMMENT '挂牌价格_标准化（元/平方米/天）',
    list_price_std_sq_m decimal(20,4) COMMENT '挂牌价格_标准化（元/平方米/月）',
    list_price_std_sq_y decimal(20,4) COMMENT '挂牌价格_标准化（元/平方米/年）',
    list_price_std_d decimal(20,4) COMMENT '挂牌价格_标准化（元/天）',
    list_price_std_m decimal(20,4) COMMENT '挂牌价格_标准化（元/月）',
    list_price_std_y decimal(20,4) COMMENT '挂牌价格_标准化（元/年）',
    
    -- 系统计算价格
    list_price_sys_m decimal(20,4) COMMENT '平均月租金_系统值（元/月）',
    list_price_sys_y decimal(20,4) COMMENT '平均年租金_系统值（元/年）',
    list_price_sys_d decimal(20,4) COMMENT '平均日租金_系统计算值（元/天）',
    list_price_sys_std_sq_d decimal(20,4) COMMENT '平均日租金_系统计算值（元/平方米/天）',
    list_price_sys_std_sq_m decimal(20,4) COMMENT '平均月租金_系统计算值（元/平方米/月）',
    list_price_sys_std_sq_y decimal(20,4) COMMENT '平均年租金_系统计算值（元/平方米/年）',
    
    -- 租赁相关
    lease_area decimal(10,4) COMMENT '拟出租面积（平方米）',
    lease_prd_type string COMMENT '租赁期类型',
    lease_prd_range_type string COMMENT '租赁期区间类型',
    lease_prd_ent1 string COMMENT '挂牌租赁期_录入值1',
    lease_prd_ent2 string COMMENT '挂牌租赁期_录入值2',
    lease_prd_end_date string COMMENT '挂牌租赁期_截止日期',
    lease_prd_std_d int COMMENT '挂牌租赁期_标准化（天）',
    lease_prd_std_m decimal(10,2) COMMENT '挂牌租赁期_标准化（月）',
    lease_prd_std_y decimal(10,2) COMMENT '挂牌租赁期_标准化（年）',
    
    -- 总价
    list_total_price_cal decimal(20,4) COMMENT '挂牌总价_计算值(元)',
    list_total_price_sys decimal(20,4) COMMENT '挂牌总价_系统计算值(元)',
    
    -- 估价相关
    is_sep_appraise string COMMENT '是否分别估价',
    appr_price_uni decimal(20,4) COMMENT '租金估价_统一估价录入值',
    appr_price_unit_uni string COMMENT '估价单位_统一估价录入值',
    appr_price_sep map<string,string> COMMENT '租金估价_分别估价录入值',
    appr_price_std_sq_d decimal(20,4) COMMENT '租金估价_标准化（元/平方米/天）',
    appr_total_price decimal(20,4) COMMENT '租金估价_总价(元)',
    
    -- 披露相关
    re_list_num string COMMENT '重新披露次序',
    is_last_list string COMMENT '是否最后一次披露',
    f_list_start_date string COMMENT '初次挂牌开始日期',
    f_list_end_date string COMMENT '初次挂牌结束日期',
    f_list_start_year string COMMENT '初次挂牌开始年份',
    f_list_end_year string COMMENT '初次挂牌结束年份',
    f_list_start_ym string COMMENT '初次挂牌开始年月',
    f_list_end_ym string COMMENT '初次挂牌结束年月',
    f_list_price_std_sq_d decimal(20,4) COMMENT '初次挂牌价格_标准化（元/平方米/天）',
    reduce_price_std_sq_d decimal(20,4) COMMENT '降价金额_标准化（元/平方米/天）',
    is_extend string COMMENT '是否延牌',
    
    -- 免租期
    free_prd_type string COMMENT '免租期类型',
    free_prd_ent1 string COMMENT '免租期_录入值1',
    free_prd_ent2 string COMMENT '免租期_录入值2',
    free_prd_std_d int COMMENT '免租期_标准化（天）',

    -- 成交相关
    deal_date string COMMENT '成交日期',
    deal_price_type string COMMENT '成交计价方式',
    deal_price_uni decimal(20,4) COMMENT '成交租金价格_统一计价录入值',
    deal_price_unit_uni string COMMENT '成交租金价格单位_统一计价录入值',
    deal_price_remark string COMMENT '成交租金价格备注',
    deal_price_sep map<string,string> COMMENT '成交租金价格_分别计价录入值',
    deal_price_std_sq_d decimal(20,4) COMMENT '成交租金价格_标准化（元/平方米/天）',
    deal_total_price decimal(20,4) COMMENT '成交租金总价_录入值（元）',
    deal_lease_area decimal(10,4) COMMENT '出租面积_成交录入（平方米）',
    deal_lease_prd_ent string COMMENT '租赁时长_成交录入',
    deal_lease_prd_std_d int COMMENT '租赁时长_成交标准化（天）',

    -- 溢价相关
    premium_vs_list_price decimal(20,4) COMMENT '溢价金额_对比挂牌单价（元/平方米/天）',
    premium_vs_list_total_price decimal(20,4) COMMENT '溢价金额_对比挂牌总价（元）',
    premium_rate_vs_list_price decimal(20,4) COMMENT '溢价率_对比挂牌单价',
    premium_vs_appr_price decimal(20,4) COMMENT '溢价金额_对比评估单价（元/平方米/天）',
    premium_vs_appr_total_price decimal(20,4) COMMENT '溢价金额_对比评估总价（元）',
    premium_rate_vs_appr_price decimal(20,4) COMMENT '溢价率_对比评估单价',
    deal_price_precontract decimal(20,4) COMMENT '上份合同的成交租金价格（元/平方米/天）',
    increase_vs_precontract decimal(20,4) COMMENT '较上份合同增值（元/平方米/天）',
    increase_rate_vs_precontract decimal(20,4) COMMENT '较上份合同增值率',

    -- 交易方式和合同
    actual_deal_methond string COMMENT '实际交易方式（成交方式）',
    bid_type string COMMENT '网络竞价类型',
    sign_date_contract string COMMENT '合同签订日期',
    effective_date_contract string COMMENT '合同生效日期',

    -- 出租方信息
    lessor_name string COMMENT '出租方名称',
    lessor_province string COMMENT '出租方所在省',
    lessor_city string COMMENT '出租方所在市',
    lessor_district string COMMENT '出租方所在区',
    lessor_reg_addr string COMMENT '出租方注册地',
    lessor_type_sys string COMMENT '出租方类型_系统值',
    lessor_type_research string COMMENT '出租方类型_研究中心',
    lessor_type_municipal string COMMENT '出租方类型_市属',
    asset_source_l2 string COMMENT '资产来源(2级)',
    asset_source_l3 string COMMENT '资产来源(3级)',
    asset_source_l4 string COMMENT '资产来源(4级)',
    asset_source_l5 string COMMENT '资产来源(5级)',
    parent_group string COMMENT '所属集团',
    approval_unit string COMMENT '项目批准单位',
    enterprise_tier_num int COMMENT '企业层级数量',
    asset_source_ent_l1 string COMMENT '资产来源（企业1级）',
    asset_source_ent_l2 string COMMENT '资产来源（企业2级）',
    asset_source_ent_l3 string COMMENT '资产来源（企业3级）',
    asset_source_ent_l4 string COMMENT '资产来源（企业4级）',
    asset_source_ent_l5 string COMMENT '资产来源（企业5级）',
    lessor_eco_nature string COMMENT '出租方经济性质',
    lessor_industry_type string COMMENT '出租方所属行业类型',
    lessor_industry string COMMENT '出租方所属行业',
    lessor_biz_scope string COMMENT '出租方经营范围',

    -- 意向承租方统计
    reg_intend_lessee_cnt int COMMENT '报名的意向承租方数量',
    mm_intend_lessee_cnt int COMMENT '交保的意向承租方数量',
    bid_intend_lessee_cnt int COMMENT '具备竞价资格的意向承租方数量',

    -- 承租方信息
    lessee_name string COMMENT '承租方名称',
    lessee_type string COMMENT '承租方类型',
    lessee_province string COMMENT '承租方所在省',
    lessee_district string COMMENT '承租方所在区',
    lessee_name_second_bid string COMMENT '竞价排名第二的意向承租方名称',
    lessee_offer_second_bid decimal(20,4) COMMENT '竞价排名第二的意向承租方报价（元/平方米/天）',
    lessee_name_third_bid string COMMENT '竞价排名第三的意向承租方名称',
    lessee_offer_third_bid decimal(20,4) COMMENT '竞价排名第三的意向承租方报价（元/平方米/天）',

    -- 北交所收入和费用
    total_revenue decimal(20,4) COMMENT '北交所总收入（元）',
    net_revenue decimal(20,4) COMMENT '北交所净收入（元）',
    lessor_service_fee decimal(20,4) COMMENT '出租方服务费金额（元）',
    lessee_service_fee decimal(20,4) COMMENT '承租方服务费金额（元）',
    lessor_memb_comm decimal(20,4) COMMENT '出租方会员分佣金额（元）',
    lessee_memb_comm decimal(20,4) COMMENT '承租方会员分佣金额（元）',
    intend_lessee_mmb_comm decimal(20,4) COMMENT '意向承租方会员分佣金额（元）',

    -- 结算相关
    remain_pay_method string COMMENT '剩余价款结算方式',
    is_foreign_curr_settle string COMMENT '是否外币结算',
    is_mm_handle_changed string COMMENT '保证金处置方式是否有变更',
    mm_handle_method string COMMENT '保证金处置方式',
    is_mm_convert_rent string COMMENT '保证金是否转成交租金总价',

    -- 时间节点
    prj_entry_date string COMMENT '项目录入日期',
    ulti_lessee_confirm_date string COMMENT '确认最终承租方日期',

    -- 成交周期_自然日
    deal_cycle_ent_list_nd int COMMENT '成交周期_录入至披露_自然日',
    deal_cycle_list_conf_nd int COMMENT '成交周期_披露至确认最终承租方_自然日',
    deal_cycle_conf_deal_nd int COMMENT '成交周期_确认最终承租方至成交_自然日',
    deal_cycle_ent_deal_nd int COMMENT '成交周期_录入至成交_自然日',
    deal_cycle_list_deal_nd int COMMENT '成交周期_披露至成交_自然日',

    -- 成交周期_工作日
    deal_cycle_ent_list_wd int COMMENT '成交周期_录入至披露_工作日',
    deal_cycle_list_conf_wd int COMMENT '成交周期_披露至确认最终承租方_工作日',
    deal_cycle_conf_deal_wd int COMMENT '成交周期_确认最终承租方至成交_工作日',
    deal_cycle_ent_deal_wd int COMMENT '成交周期_录入至成交_工作日',
    deal_cycle_list_deal_wd int COMMENT '成交周期_披露至成交_工作日',

    -- 项目关注度
    prj_view_cnt int COMMENT '项目围观次数',
    prj_follow_cnt int COMMENT '项目关注用户数',

    -- 补充字段
    start_lease_type string COMMENT '起租类型',
    start_lease_date string COMMENT '起租日',
    price_adjust_method string COMMENT '租金调整方式',
    list_price_notes string COMMENT '租金挂牌价补充说明',
    rent_mm_pay_require string COMMENT '租金及押金支付要求',
    is_rent_cover_other_fees string COMMENT '租金挂牌价是否含其他费用',
    lessee_resp_fees string COMMENT '承租方需承担费用',
    no_intend_lessee_opt string COMMENT '未征集到意向承租方选项',
    is_improve_allowed string COMMENT '是否允许装修改造',
    is_intend_lessee_biz_inv string COMMENT '是否涉及意向承租方经营范围',
    is_appraise_disclosed string COMMENT '估价是否外网披露',
    other_list_items string COMMENT '其他披露事项',
    annual_days_sys int COMMENT '年天数（天）_系统值',
    monthly_days_sys int COMMENT '月天数（天）_系统值'
)
COMMENT '北交所房屋租赁项目宽表'
PARTITIONED BY (dt string COMMENT '数据日期')
ROW FORMAT DELIMITED FIELDS TERMINATED BY '\001'
LINES TERMINATED BY '\n'
STORED AS ORC 


CREATE TABLE `STD.STD_BJHL_TZCCZ_ZCCZXM_FBGJNR_D`(
  `id` decimal(16,0) COMMENT 'ID', 
  `lc_warehouse` string COMMENT '楼层/仓库 (原字段: lcck)', 
  `unit_price` string COMMENT '单价 (原字段: dj)', 
  `area` string COMMENT '面积 (原字段: mj)', 
  `regional_pricing` string COMMENT '区域分别计价 (原字段: fbjjxx)', 
  `rental_area` decimal(16,2) COMMENT '分别计价出租面积(平方米) (原字段: fbjjczmj)', 
  `valuation_method` decimal(12,0) COMMENT '分别计价估价方式 (原字段: fbjjgpfs)', 
  `valuation_unit` decimal(12,0) COMMENT '分别计价估价单位 (原字段: fbjjgpjdw)', 
  `other_valuation_unit` string COMMENT '其他分别计价估价单位 (原字段: qtfbjjgpjdw)', 
  `valuation_remark` string COMMENT '分别计价估价备注 (原字段: fbjjgpjbz)', 
  `tzccz_zcczxm_id` decimal(16,0) COMMENT 'TZCCZ_ZCCZXM_ID', 
  `valuation_price` decimal(16,4) COMMENT '分别计价估价价格 (原字段: fbjjgpj)')
PARTITIONED BY ( 
  `dt` string)
ROW FORMAT DELIMITED FIELDS TERMINATED BY '\001'
LINES TERMINATED BY '\n'
STORED AS ORC